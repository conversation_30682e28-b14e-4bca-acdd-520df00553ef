{"actions": [], "allow_auto_repeat": 1, "allow_import": 1, "autoname": "naming_series:", "creation": "2013-05-24 19:29:09", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["title", "naming_series", "customer", "tax_id", "customer_name", "column_break1", "posting_date", "posting_time", "set_posting_time", "column_break_10", "company", "amended_from", "is_return", "issue_credit_note", "return_against", "accounting_dimensions_section", "cost_center", "column_break_18", "project", "dimension_col_break", "currency_and_price_list", "currency", "conversion_rate", "col_break23", "selling_price_list", "price_list_currency", "plc_conversion_rate", "ignore_pricing_rule", "items_section", "scan_barcode", "col_break_warehouse", "set_warehouse", "set_target_warehouse", "section_break_30", "items", "section_break_31", "total_qty", "total_net_weight", "column_break_35", "base_total", "base_net_total", "column_break_33", "total", "net_total", "taxes_section", "tax_category", "taxes_and_charges", "column_break_43", "shipping_rule", "column_break_39", "incoterm", "named_place", "section_break_41", "taxes", "section_break_44", "base_total_taxes_and_charges", "column_break_47", "total_taxes_and_charges", "totals", "base_grand_total", "base_rounding_adjustment", "base_rounded_total", "base_in_words", "column_break3", "grand_total", "rounding_adjustment", "rounded_total", "in_words", "disable_rounded_total", "section_break_49", "apply_discount_on", "base_discount_amount", "column_break_51", "additional_discount_percentage", "discount_amount", "sec_tax_breakup", "other_charges_calculation", "packing_list", "packed_items", "product_bundle_help", "pricing_rule_details", "pricing_rules", "address_and_contact_tab", "contact_info", "customer_address", "address_display", "col_break21", "contact_person", "contact_display", "contact_mobile", "contact_email", "shipping_address_section", "shipping_address_name", "shipping_address", "column_break_95", "dispatch_address_name", "dispatch_address", "company_address_section", "company_address", "company_address_display", "column_break_101", "company_contact_person", "terms_tab", "tc_name", "terms", "more_info_tab", "section_break_83", "per_billed", "status", "column_break_112", "per_installed", "installation_status", "column_break_89", "per_returned", "transporter_info", "transporter", "driver", "lr_no", "vehicle_no", "col_break34", "transporter_name", "driver_name", "lr_date", "customer_po_details", "po_no", "column_break_17", "po_date", "sales_team_section_break", "sales_partner", "amount_eligible_for_commission", "column_break7", "commission_rate", "total_commission", "section_break1", "sales_team", "subscription_section", "auto_repeat", "printing_details", "letter_head", "print_without_amount", "group_same_items", "column_break_88", "select_print_heading", "language", "more_info", "is_internal_customer", "represents_company", "inter_company_reference", "customer_group", "territory", "source", "campaign", "column_break5", "excise_page", "instructions", "connections_tab"], "fields": [{"allow_on_submit": 1, "default": "{customer_name}", "fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "no_copy": 1, "print_hide": 1}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "MAT-DN-.YYYY.-\nMAT-DN-RET-.YYYY.-", "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "customer", "fieldtype": "Link", "in_global_search": 1, "in_standard_filter": 1, "label": "Customer", "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer", "print_hide": 1, "reqd": 1, "search_index": 1}, {"bold": 1, "depends_on": "customer", "fetch_from": "customer.customer_name", "fieldname": "customer_name", "fieldtype": "Data", "in_global_search": 1, "label": "Customer Name", "read_only": 1}, {"fieldname": "column_break1", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "amended_from", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Amended From", "no_copy": 1, "oldfieldname": "amended_from", "oldfieldtype": "Data", "options": "Delivery Note", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "company", "fieldtype": "Link", "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "print_hide": 1, "print_width": "150px", "remember_last_selected_value": 1, "reqd": 1, "width": "150px"}, {"default": "Today", "fieldname": "posting_date", "fieldtype": "Date", "label": "Date", "no_copy": 1, "oldfieldname": "posting_date", "oldfieldtype": "Date", "print_width": "100px", "reqd": 1, "search_index": 1, "width": "100px"}, {"default": "Now", "fieldname": "posting_time", "fieldtype": "Time", "label": "Posting Time", "oldfieldname": "posting_time", "oldfieldtype": "Time", "print_hide": 1, "print_width": "100px", "reqd": 1, "width": "100px"}, {"default": "0", "depends_on": "eval:doc.docstatus==0", "fieldname": "set_posting_time", "fieldtype": "Check", "label": "Edit Posting Date and Time", "print_hide": 1}, {"default": "0", "fieldname": "is_return", "fieldtype": "Check", "label": "Is Return", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"default": "0", "depends_on": "is_return", "fieldname": "issue_credit_note", "fieldtype": "Check", "label": "Issue Credit Note"}, {"depends_on": "is_return", "fieldname": "return_against", "fieldtype": "Link", "label": "Return Against Delivery Note", "no_copy": 1, "options": "Delivery Note", "print_hide": 1, "read_only": 1, "search_index": 1}, {"collapsible": 1, "fieldname": "customer_po_details", "fieldtype": "Section Break", "label": "Customer PO Details"}, {"allow_on_submit": 1, "fieldname": "po_no", "fieldtype": "Small Text", "label": "Customer's Purchase Order No", "no_copy": 1, "oldfieldname": "po_no", "oldfieldtype": "Data", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"fieldname": "column_break_17", "fieldtype": "Column Break"}, {"fieldname": "po_date", "fieldtype": "Date", "label": "Customer's Purchase Order Date", "oldfieldname": "po_date", "oldfieldtype": "Data", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"depends_on": "customer", "fieldname": "contact_info", "fieldtype": "Section Break", "label": "Billing Address", "options": "fa fa-bullhorn"}, {"fieldname": "shipping_address_name", "fieldtype": "Link", "label": "Shipping Address", "options": "Address", "print_hide": 1}, {"fieldname": "shipping_address", "fieldtype": "Small Text", "label": "Shipping Address", "read_only": 1}, {"fieldname": "contact_person", "fieldtype": "Link", "label": "Contact Person", "options": "Contact", "print_hide": 1}, {"fieldname": "contact_display", "fieldtype": "Small Text", "in_global_search": 1, "label": "Contact", "read_only": 1}, {"fieldname": "contact_mobile", "fieldtype": "Small Text", "hidden": 1, "label": "Mobile No", "options": "Phone", "read_only": 1}, {"fieldname": "contact_email", "fieldtype": "Data", "hidden": 1, "label": "Contact Email", "options": "Email", "print_hide": 1, "read_only": 1}, {"fieldname": "col_break21", "fieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"depends_on": "customer", "fieldname": "customer_address", "fieldtype": "Link", "label": "Billing Address Name", "options": "Address", "print_hide": 1}, {"fieldname": "tax_id", "fieldtype": "Data", "label": "Tax Id", "read_only": 1}, {"fieldname": "address_display", "fieldtype": "Small Text", "label": "Billing Address", "read_only": 1}, {"fieldname": "company_address", "fieldtype": "Link", "label": "Company Address Name", "options": "Address"}, {"fieldname": "company_address_display", "fieldtype": "Small Text", "label": "Company Address", "read_only": 1}, {"collapsible": 1, "fieldname": "currency_and_price_list", "fieldtype": "Section Break", "label": "Currency and Price List", "options": "fa fa-tag"}, {"fieldname": "currency", "fieldtype": "Link", "label": "<PERSON><PERSON><PERSON><PERSON>", "oldfieldname": "currency", "oldfieldtype": "Select", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "reqd": 1}, {"description": "Rate at which customer's currency is converted to company's base currency", "fieldname": "conversion_rate", "fieldtype": "Float", "label": "Exchange Rate", "oldfieldname": "conversion_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "precision": "9", "print_hide": 1, "reqd": 1}, {"fieldname": "col_break23", "fieldtype": "Column Break"}, {"fieldname": "selling_price_list", "fieldtype": "Link", "label": "Price List", "oldfieldname": "price_list_name", "oldfieldtype": "Select", "options": "Price List", "print_hide": 1, "reqd": 1}, {"fieldname": "price_list_currency", "fieldtype": "Link", "label": "Price List Currency", "options": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1, "reqd": 1}, {"description": "Rate at which Price list currency is converted to company's base currency", "fieldname": "plc_conversion_rate", "fieldtype": "Float", "label": "Price List Exchange Rate", "precision": "9", "print_hide": 1, "reqd": 1}, {"default": "0", "fieldname": "ignore_pricing_rule", "fieldtype": "Check", "label": "Ignore Pricing Rule", "permlevel": 1, "print_hide": 1}, {"fieldname": "set_warehouse", "fieldtype": "Link", "label": "Set Source Warehouse", "options": "Warehouse", "print_hide": 1}, {"fieldname": "col_break_warehouse", "fieldtype": "Column Break"}, {"fieldname": "items_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Items", "oldfieldtype": "Section Break", "options": "fa fa-shopping-cart"}, {"fieldname": "scan_barcode", "fieldtype": "Data", "label": "Scan Barcode", "options": "Barcode"}, {"allow_bulk_edit": 1, "fieldname": "items", "fieldtype": "Table", "label": "Delivery Note Item", "oldfieldname": "delivery_note_details", "oldfieldtype": "Table", "options": "Delivery Note Item", "reqd": 1}, {"fieldname": "pricing_rule_details", "fieldtype": "Section Break", "label": "Pricing Rules"}, {"fieldname": "pricing_rules", "fieldtype": "Table", "label": "Pricing Rule Detail", "options": "Pricing Rule Detail", "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "packed_items", "depends_on": "packed_items", "fieldname": "packing_list", "fieldtype": "Section Break", "label": "Packing List", "oldfieldtype": "Section Break", "options": "fa fa-suitcase", "print_hide": 1}, {"depends_on": "packed_items", "fieldname": "packed_items", "fieldtype": "Table", "label": "Packed Items", "oldfieldname": "packing_details", "oldfieldtype": "Table", "options": "Packed Item", "print_hide": 1}, {"fieldname": "product_bundle_help", "fieldtype": "HTML", "label": "Product Bundle Help", "print_hide": 1}, {"fieldname": "section_break_31", "fieldtype": "Section Break"}, {"fieldname": "total_qty", "fieldtype": "Float", "label": "Total Quantity", "read_only": 1}, {"fieldname": "base_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total (Company Currency)", "oldfieldname": "net_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "column_break_33", "fieldtype": "Column Break"}, {"fieldname": "total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total", "options": "currency", "read_only": 1}, {"fieldname": "net_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Total", "options": "currency", "print_hide": 1, "read_only": 1}, {"depends_on": "total_net_weight", "fieldname": "total_net_weight", "fieldtype": "Float", "label": "Total Net Weight", "print_hide": 1, "read_only": 1}, {"fieldname": "taxes_section", "fieldtype": "Section Break", "hide_border": 1, "label": "Taxes and Charges", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "tax_category", "fieldtype": "Link", "label": "Tax Category", "options": "Tax Category", "print_hide": 1}, {"fieldname": "column_break_39", "fieldtype": "Column Break"}, {"fieldname": "shipping_rule", "fieldtype": "Link", "label": "Shipping Rule", "oldfieldtype": "<PERSON><PERSON>", "options": "Shipping Rule", "print_hide": 1}, {"fieldname": "section_break_41", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "taxes_and_charges", "fieldtype": "Link", "label": "Sales Taxes and Charges Template", "oldfieldname": "charge", "oldfieldtype": "Link", "options": "Sales Taxes and Charges Template", "print_hide": 1}, {"fieldname": "taxes", "fieldtype": "Table", "label": "Sales Taxes and Charges", "oldfieldname": "other_charges", "oldfieldtype": "Table", "options": "Sales Taxes and Charges"}, {"collapsible": 1, "fieldname": "sec_tax_breakup", "fieldtype": "Section Break", "label": "Tax Breakup"}, {"fieldname": "other_charges_calculation", "fieldtype": "Text Editor", "label": "Taxes and Charges Calculation", "no_copy": 1, "oldfieldtype": "HTML", "print_hide": 1, "read_only": 1}, {"fieldname": "section_break_44", "fieldtype": "Section Break"}, {"fieldname": "base_total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges (Company Currency)", "oldfieldname": "other_charges_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "column_break_47", "fieldtype": "Column Break"}, {"fieldname": "total_taxes_and_charges", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Taxes and Charges", "options": "currency", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "fieldname": "section_break_49", "fieldtype": "Section Break", "label": "Additional Discount"}, {"default": "Grand Total", "fieldname": "apply_discount_on", "fieldtype": "Select", "label": "Apply Additional Discount On", "options": "\nGrand Total\nNet Total", "print_hide": 1}, {"fieldname": "base_discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_51", "fieldtype": "Column Break"}, {"fieldname": "additional_discount_percentage", "fieldtype": "Float", "label": "Additional Discount Percentage", "print_hide": 1}, {"fieldname": "discount_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Additional Discount Amount", "options": "currency", "print_hide": 1}, {"fieldname": "totals", "fieldtype": "Section Break", "label": "Totals", "oldfieldtype": "Section Break", "options": "fa fa-money"}, {"fieldname": "base_grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Grand Total (Company Currency)", "oldfieldname": "grand_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment (Company Currency)", "no_copy": 1, "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "base_rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total (Company Currency)", "oldfieldname": "rounded_total", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"description": "In Words will be visible once you save the Delivery Note.", "fieldname": "base_in_words", "fieldtype": "Data", "label": "In Words (Company Currency)", "length": 240, "oldfieldname": "in_words", "oldfieldtype": "Data", "print_hide": 1, "print_width": "200px", "read_only": 1, "width": "200px"}, {"fieldname": "column_break3", "fieldtype": "Column Break", "oldfieldtype": "Column Break"}, {"fieldname": "grand_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Grand Total", "oldfieldname": "grand_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "150px", "read_only": 1, "width": "150px"}, {"depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounding_adjustment", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounding Adjustment", "no_copy": 1, "options": "currency", "print_hide": 1, "read_only": 1}, {"bold": 1, "depends_on": "eval:!doc.disable_rounded_total", "fieldname": "rounded_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Rounded Total", "oldfieldname": "rounded_total_export", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "currency", "print_width": "150px", "read_only": 1, "width": "150px"}, {"description": "In Words (Export) will be visible once you save the Delivery Note.", "fieldname": "in_words", "fieldtype": "Data", "label": "In Words", "length": 240, "oldfieldname": "in_words_export", "oldfieldtype": "Data", "print_hide": 1, "print_width": "150px", "read_only": 1, "width": "150px"}, {"fieldname": "tc_name", "fieldtype": "Link", "label": "Terms", "oldfieldname": "tc_name", "oldfieldtype": "Link", "options": "Terms and Conditions", "print_hide": 1}, {"fieldname": "terms", "fieldtype": "Text Editor", "label": "Terms and Conditions Details", "oldfieldname": "terms", "oldfieldtype": "Text Editor"}, {"collapsible": 1, "collapsible_depends_on": "transporter", "fieldname": "transporter_info", "fieldtype": "Section Break", "label": "Transporter Info", "options": "fa fa-truck", "print_hide": 1}, {"fieldname": "transporter", "fieldtype": "Link", "label": "Transporter", "options": "Supplier", "print_hide": 1, "print_width": "150px", "width": "150px"}, {"fieldname": "driver", "fieldtype": "Link", "label": "Driver", "options": "Driver", "print_hide": 1}, {"fieldname": "lr_no", "fieldtype": "Data", "label": "Transport Receipt No", "oldfieldname": "lr_no", "oldfieldtype": "Data", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"fieldname": "vehicle_no", "fieldtype": "Data", "label": "Vehicle No", "print_hide": 1}, {"fieldname": "col_break34", "fieldtype": "Column Break", "print_width": "50%", "width": "50%"}, {"fetch_from": "transporter.supplier_name", "fieldname": "transporter_name", "fieldtype": "Data", "label": "Transporter Name", "print_hide": 1, "read_only": 1}, {"fetch_from": "driver.full_name", "fieldname": "driver_name", "fieldtype": "Data", "label": "Driver Name", "print_hide": 1}, {"default": "Today", "fieldname": "lr_date", "fieldtype": "Date", "label": "Transport Receipt Date", "oldfieldname": "lr_date", "oldfieldtype": "Date", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"collapsible": 1, "fieldname": "more_info", "fieldtype": "Section Break", "label": "Additional Info", "oldfieldtype": "Section Break", "options": "fa fa-file-text", "print_hide": 1}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "oldfieldname": "project", "oldfieldtype": "Link", "options": "Project"}, {"fieldname": "campaign", "fieldtype": "Link", "label": "Campaign", "oldfieldname": "campaign", "oldfieldtype": "Link", "options": "Campaign", "print_hide": 1}, {"fieldname": "source", "fieldtype": "Link", "label": "Source", "oldfieldname": "source", "oldfieldtype": "Select", "options": "Lead Source", "print_hide": 1}, {"fieldname": "column_break5", "fieldtype": "Column Break", "oldfieldtype": "Column Break", "print_hide": 1, "print_width": "50%", "width": "50%"}, {"fieldname": "per_billed", "fieldtype": "Percent", "label": "% Amount Billed", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"fieldname": "customer_group", "fieldtype": "Link", "hidden": 1, "label": "Customer Group", "options": "Customer Group", "print_hide": 1}, {"fieldname": "territory", "fieldtype": "Link", "label": "Territory", "options": "Territory", "print_hide": 1}, {"collapsible": 1, "fieldname": "printing_details", "fieldtype": "Section Break", "label": "Print Settings"}, {"allow_on_submit": 1, "fieldname": "letter_head", "fieldtype": "Link", "label": "Letter Head", "oldfieldname": "letter_head", "oldfieldtype": "Link", "options": "Letter Head", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "select_print_heading", "fieldtype": "Link", "label": "Print Heading", "no_copy": 1, "oldfieldname": "select_print_heading", "oldfieldtype": "Link", "options": "Print Heading", "print_hide": 1, "report_hide": 1}, {"fieldname": "language", "fieldtype": "Data", "label": "Print Language", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_88", "fieldtype": "Column Break"}, {"allow_on_submit": 1, "default": "0", "fieldname": "print_without_amount", "fieldtype": "Check", "label": "Print Without Amount", "oldfieldname": "print_without_amount", "oldfieldtype": "Check", "print_hide": 1}, {"allow_on_submit": 1, "default": "0", "fieldname": "group_same_items", "fieldtype": "Check", "label": "Group same items", "print_hide": 1}, {"collapsible": 1, "fieldname": "section_break_83", "fieldtype": "Section Break", "label": "Status"}, {"default": "Draft", "fieldname": "status", "fieldtype": "Select", "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "\nDraft\nTo Bill\nCompleted\nReturn Issued\nCancelled\nClosed", "print_hide": 1, "print_width": "150px", "read_only": 1, "reqd": 1, "search_index": 1, "width": "150px"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "per_installed", "fieldtype": "Percent", "in_list_view": 1, "label": "% Installed", "no_copy": 1, "oldfieldname": "per_installed", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "read_only": 1}, {"fieldname": "installation_status", "fieldtype": "Select", "hidden": 1, "label": "Installation Status", "print_hide": 1}, {"fieldname": "column_break_89", "fieldtype": "Column Break"}, {"fieldname": "excise_page", "fieldtype": "Data", "hidden": 1, "label": "Excise Page Number", "oldfieldname": "excise_page", "oldfieldtype": "Data", "print_hide": 1}, {"fieldname": "instructions", "fieldtype": "Text", "label": "Instructions", "oldfieldname": "instructions", "oldfieldtype": "Text"}, {"fieldname": "subscription_section", "fieldtype": "Section Break", "label": "Subscription Section"}, {"fieldname": "auto_repeat", "fieldtype": "Link", "label": "Auto Repeat", "no_copy": 1, "options": "Auto Repeat", "print_hide": 1, "read_only": 1}, {"collapsible": 1, "collapsible_depends_on": "total_commission", "fieldname": "sales_team_section_break", "fieldtype": "Section Break", "label": "Commission", "oldfieldtype": "Section Break", "options": "fa fa-group", "print_hide": 1}, {"fieldname": "sales_partner", "fieldtype": "Link", "label": "Sales Partner", "oldfieldname": "sales_partner", "oldfieldtype": "Link", "options": "Sales Partner", "print_hide": 1, "print_width": "150px", "width": "150px"}, {"fieldname": "column_break7", "fieldtype": "Column Break", "print_hide": 1, "print_width": "50%", "width": "50%"}, {"fetch_from": "sales_partner.commission_rate", "fetch_if_empty": 1, "fieldname": "commission_rate", "fieldtype": "Float", "label": "Commission Rate (%)", "oldfieldname": "commission_rate", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "print_hide": 1, "print_width": "100px", "width": "100px"}, {"fieldname": "total_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Commission", "oldfieldname": "total_commission", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency", "print_hide": 1}, {"collapsible": 1, "collapsible_depends_on": "sales_team", "fieldname": "section_break1", "fieldtype": "Section Break", "label": "Sales Team", "print_hide": 1}, {"allow_on_submit": 1, "fieldname": "sales_team", "fieldtype": "Table", "label": "Sales Team", "oldfieldname": "sales_team", "oldfieldtype": "Table", "options": "Sales Team", "print_hide": 1}, {"default": "0", "fetch_from": "customer.is_internal_customer", "fieldname": "is_internal_customer", "fieldtype": "Check", "label": "Is Internal Customer", "read_only": 1}, {"fieldname": "inter_company_reference", "fieldtype": "Link", "label": "Inter Company Reference", "options": "Purchase Receipt"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "per_returned", "fieldtype": "Percent", "label": "% Returned", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"depends_on": "eval: doc.is_internal_customer", "fieldname": "set_target_warehouse", "fieldtype": "Link", "ignore_user_permissions": 1, "in_standard_filter": 1, "label": "Set Target Warehouse", "no_copy": 1, "oldfieldname": "to_warehouse", "oldfieldtype": "Link", "options": "Warehouse", "print_hide": 1}, {"description": "Company which internal customer represents.", "fetch_from": "customer.represents_company", "fieldname": "represents_company", "fieldtype": "Link", "ignore_user_permissions": 1, "label": "Represents Company", "options": "Company", "read_only": 1}, {"default": "0", "depends_on": "grand_total", "fieldname": "disable_rounded_total", "fieldtype": "Check", "label": "Disable Rounded Total"}, {"fieldname": "dispatch_address_name", "fieldtype": "Link", "label": "Dispatch Address Name", "options": "Address", "print_hide": 1}, {"depends_on": "dispatch_address_name", "fieldname": "dispatch_address", "fieldtype": "Small Text", "label": "Dispatch Address", "print_hide": 1, "read_only": 1}, {"fieldname": "amount_eligible_for_commission", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount Eligible for Commission", "read_only": 1}, {"collapsible": 1, "fieldname": "accounting_dimensions_section", "fieldtype": "Section Break", "label": "Accounting Dimensions"}, {"fieldname": "cost_center", "fieldtype": "Link", "label": "Cost Center", "options": "Cost Center"}, {"fieldname": "dimension_col_break", "fieldtype": "Column Break"}, {"fieldname": "address_and_contact_tab", "fieldtype": "Tab Break", "label": "Address & Contact"}, {"fieldname": "terms_tab", "fieldtype": "Tab Break", "label": "Terms"}, {"fieldname": "more_info_tab", "fieldtype": "Tab Break", "label": "More Info"}, {"fieldname": "connections_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "section_break_30", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "column_break_35", "fieldtype": "Column Break"}, {"fieldname": "column_break_43", "fieldtype": "Column Break"}, {"fieldname": "shipping_address_section", "fieldtype": "Section Break", "label": "Shipping Address"}, {"fieldname": "column_break_95", "fieldtype": "Column Break"}, {"fieldname": "company_address_section", "fieldtype": "Section Break", "label": "Company Address"}, {"fieldname": "column_break_101", "fieldtype": "Column Break"}, {"fieldname": "column_break_112", "fieldtype": "Column Break"}, {"fieldname": "column_break_18", "fieldtype": "Column Break"}, {"fieldname": "incoterm", "fieldtype": "Link", "label": "Incoterm", "options": "Incoterm"}, {"depends_on": "incoterm", "fieldname": "named_place", "fieldtype": "Data", "label": "Named Place"}, {"fieldname": "company_contact_person", "fieldtype": "Link", "label": "Company Contact Person", "options": "Contact", "print_hide": 1}], "icon": "fa fa-truck", "idx": 146, "is_submittable": 1, "links": [], "modified": "2025-08-04 19:20:47.724218", "modified_by": "Administrator", "module": "Stock", "name": "Delivery Note", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "submit": 1, "write": 1}, {"read": 1, "report": 1, "role": "Accounts User"}, {"permlevel": 1, "read": 1, "role": "Stock Manager", "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Delivery User", "share": 1, "submit": 1, "write": 1}, {"amend": 1, "cancel": 1, "create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Delivery Manager", "share": 1, "submit": 1, "write": 1}], "row_format": "Dynamic", "search_fields": "status,customer,customer_name, territory,base_grand_total", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "timeline_field": "customer", "title_field": "title", "track_changes": 1, "track_seen": 1}