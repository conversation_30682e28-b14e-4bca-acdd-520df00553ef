{"actions": [], "creation": "2016-09-26 02:19:21.642081", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["item_code", "column_break_2", "item_name", "quantity_and_rate", "stock_qty", "rate", "amount", "column_break_6", "stock_uom", "base_rate", "base_amount"], "fields": [{"fieldname": "item_code", "fieldtype": "Link", "in_list_view": 1, "label": "Item Code", "options": "<PERSON><PERSON>", "reqd": 1}, {"fieldname": "item_name", "fieldtype": "Data", "in_list_view": 1, "label": "Item Name"}, {"fieldname": "quantity_and_rate", "fieldtype": "Section Break", "label": "Quantity and Rate"}, {"fieldname": "stock_qty", "fieldtype": "Float", "in_list_view": 1, "label": "Qty", "non_negative": 1, "reqd": 1}, {"fieldname": "rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Rate", "non_negative": 1, "options": "currency"}, {"fieldname": "amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Amount", "options": "currency", "read_only": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "stock_uom", "fieldtype": "Link", "label": "Stock UOM", "options": "UOM", "read_only": 1}, {"fieldname": "base_rate", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Rate (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "base_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Basic Amount (Company Currency)", "options": "Company:company:default_currency", "print_hide": 1, "read_only": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}], "istable": 1, "links": [], "modified": "2025-07-31 16:21:44.047007", "modified_by": "Administrator", "module": "Manufacturing", "name": "BOM Scrap Item", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}