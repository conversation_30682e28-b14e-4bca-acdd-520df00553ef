{"actions": [], "creation": "2015-12-23 21:31:52.699821", "doctype": "DocType", "editable_grid": 1, "field_order": ["payment_gateway", "payment_channel", "company", "is_default", "column_break_4", "payment_account", "currency", "payment_request_message", "message", "message_examples"], "fields": [{"fieldname": "payment_gateway", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Gateway", "options": "Payment Gateway", "reqd": 1}, {"default": "0", "fieldname": "is_default", "fieldtype": "Check", "label": "<PERSON>"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "payment_account", "fieldtype": "Link", "in_list_view": 1, "label": "Payment Account", "options": "Account", "reqd": 1}, {"fetch_from": "payment_account.account_currency", "fieldname": "currency", "fieldtype": "Read Only", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"depends_on": "eval: doc.payment_channel !== \"Phone\"", "fieldname": "payment_request_message", "fieldtype": "Section Break"}, {"default": "Please click on the link below to make your payment", "fieldname": "message", "fieldtype": "Small Text", "label": "Default Payment Request Message"}, {"fieldname": "message_examples", "fieldtype": "HTML", "label": "Message Examples", "options": "<pre><h5>Message Example</h5>\n\n&lt;p&gt; Thank You for being a part of {{ doc.company }}! We hope you are enjoying the service.&lt;/p&gt;\n\n&lt;p&gt; Please find enclosed the E Bill statement. The outstanding amount is {{ doc.grand_total }}.&lt;/p&gt;\n\n&lt;p&gt; We don't want you to be spending time running around in order to pay for your Bill.<br>After all, life is beautiful and the time you have in hand should be spent to enjoy it!<br>So here are our little ways to help you get more time for life! &lt;/p&gt;\n\n&lt;a href=\"{{ payment_url }}\"&gt; click here to pay &lt;/a&gt;\n\n</pre>\n"}, {"default": "Email", "fieldname": "payment_channel", "fieldtype": "Select", "label": "Payment Channel", "options": "\nEmail\nPhone"}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "label": "Company", "options": "Company", "print_hide": 1, "remember_last_selected_value": 1, "reqd": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-14 16:49:55.210352", "modified_by": "Administrator", "module": "Accounts", "name": "Payment Gateway Account", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC"}