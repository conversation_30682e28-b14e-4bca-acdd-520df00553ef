{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "Prompt", "creation": "2018-04-13 18:42:06.431683", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["category_details_section", "category_name", "round_off_tax_amount", "column_break_2", "consider_party_ledger_amount", "tax_on_excess_amount", "section_break_8", "rates", "section_break_7", "accounts"], "fields": [{"fieldname": "category_name", "fieldtype": "Data", "in_list_view": 1, "label": "Category Name", "show_days": 1, "show_seconds": 1}, {"fieldname": "section_break_8", "fieldtype": "Section Break", "label": "Tax Withholding Rates", "show_days": 1, "show_seconds": 1}, {"fieldname": "rates", "fieldtype": "Table", "label": "Rates", "options": "Tax Withholding Rate", "reqd": 1, "show_days": 1, "show_seconds": 1}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Account Details", "show_days": 1, "show_seconds": 1}, {"fieldname": "accounts", "fieldtype": "Table", "label": "Accounts", "options": "Tax Withholding Account", "reqd": 1, "show_days": 1, "show_seconds": 1}, {"fieldname": "category_details_section", "fieldtype": "Section Break", "label": "Category Details", "show_days": 1, "show_seconds": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break", "show_days": 1, "show_seconds": 1}, {"default": "0", "description": "Only payment entries with apply tax withholding unchecked will be considered for checking cumulative threshold breach", "fieldname": "consider_party_ledger_amount", "fieldtype": "Check", "label": "Consider Entire Party Ledger Amount", "show_days": 1, "show_seconds": 1}, {"default": "0", "description": "Tax will be withheld only for amount exceeding the cumulative threshold", "fieldname": "tax_on_excess_amount", "fieldtype": "Check", "label": "Only Deduct Tax On Excess Amount ", "show_days": 1, "show_seconds": 1}, {"description": "Checking this will round off the tax amount to the nearest integer", "fieldname": "round_off_tax_amount", "fieldtype": "Check", "label": "Round Off Tax Amount", "show_days": 1, "show_seconds": 1}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-30 07:13:51.785735", "modified_by": "Administrator", "module": "Accounts", "name": "Tax Withholding Category", "naming_rule": "Set by user", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}